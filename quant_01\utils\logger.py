"""
日志系统

基于Loguru的高性能日志系统。
"""

import sys
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger
from ..core.config.global_config import GlobalConfig

# 全局日志配置
_logger_initialized = False
_loggers: Dict[str, Any] = {}


def init_logger(config: Optional[GlobalConfig] = None):
    """
    初始化日志系统
    
    Args:
        config: 全局配置
    """
    global _logger_initialized
    
    if _logger_initialized:
        return
    
    if config is None:
        config = GlobalConfig.create_default()
    
    log_config = config.logging
    
    # 移除默认处理器
    logger.remove()
    
    # 添加控制台处理器
    if log_config.console_enabled:
        logger.add(
            sys.stderr,
            level=log_config.level,
            format=log_config.format,
            colorize=log_config.console_colorize,
            backtrace=True,
            diagnose=True
        )
    
    # 添加文件处理器
    if log_config.file_enabled:
        # 确保日志目录存在
        log_path = Path(log_config.file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_config.file_path,
            level=log_config.level,
            format=log_config.format,
            rotation=log_config.file_rotation,
            retention=log_config.file_retention,
            compression="zip",
            backtrace=True,
            diagnose=True
        )
    
    # 添加结构化日志处理器（可选）
    if log_config.structured:
        structured_path = log_path.parent / "structured.log"
        logger.add(
            str(structured_path),
            level=log_config.level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}",
            serialize=log_config.json_format,
            rotation="100 MB",
            retention="30 days"
        )
    
    _logger_initialized = True
    logger.info("日志系统初始化完成")


def get_logger(name: str = None) -> Any:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    if not _logger_initialized:
        init_logger()
    
    if name is None:
        return logger
    
    if name not in _loggers:
        _loggers[name] = logger.bind(name=name)
    
    return _loggers[name]


def set_log_level(level: str):
    """
    设置日志级别
    
    Args:
        level: 日志级别
    """
    logger.remove()
    init_logger()
    logger.info(f"日志级别已设置为: {level}")


def add_file_handler(
    file_path: str,
    level: str = "INFO",
    format_string: Optional[str] = None,
    rotation: str = "100 MB",
    retention: str = "30 days"
):
    """
    添加文件处理器
    
    Args:
        file_path: 文件路径
        level: 日志级别
        format_string: 格式字符串
        rotation: 轮转设置
        retention: 保留设置
    """
    if format_string is None:
        format_string = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    
    # 确保目录存在
    Path(file_path).parent.mkdir(parents=True, exist_ok=True)
    
    logger.add(
        file_path,
        level=level,
        format=format_string,
        rotation=rotation,
        retention=retention,
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    logger.info(f"文件处理器已添加: {file_path}")


def add_json_handler(
    file_path: str,
    level: str = "INFO",
    rotation: str = "100 MB",
    retention: str = "30 days"
):
    """
    添加JSON格式处理器
    
    Args:
        file_path: 文件路径
        level: 日志级别
        rotation: 轮转设置
        retention: 保留设置
    """
    # 确保目录存在
    Path(file_path).parent.mkdir(parents=True, exist_ok=True)
    
    logger.add(
        file_path,
        level=level,
        serialize=True,
        rotation=rotation,
        retention=retention,
        compression="zip"
    )
    
    logger.info(f"JSON处理器已添加: {file_path}")


def log_performance(func_name: str, execution_time: float, **kwargs):
    """
    记录性能日志
    
    Args:
        func_name: 函数名称
        execution_time: 执行时间
        **kwargs: 其他参数
    """
    perf_logger = get_logger("performance")
    
    log_data = {
        "function": func_name,
        "execution_time": execution_time,
        **kwargs
    }
    
    perf_logger.info("Performance", **log_data)


def log_trade(trade_data: Dict[str, Any]):
    """
    记录交易日志
    
    Args:
        trade_data: 交易数据
    """
    trade_logger = get_logger("trade")
    trade_logger.info("Trade executed", **trade_data)


def log_signal(signal_data: Dict[str, Any]):
    """
    记录信号日志
    
    Args:
        signal_data: 信号数据
    """
    signal_logger = get_logger("signal")
    signal_logger.info("Signal generated", **signal_data)


def log_error(error: Exception, context: Optional[Dict[str, Any]] = None):
    """
    记录错误日志
    
    Args:
        error: 异常对象
        context: 上下文信息
    """
    error_logger = get_logger("error")
    
    if context:
        error_logger.error(f"Error occurred: {error}", **context)
    else:
        error_logger.error(f"Error occurred: {error}")


class StructuredLogger:
    """结构化日志器"""
    
    def __init__(self, name: str):
        self.name = name
        self.logger = get_logger(name)
    
    def log(self, level: str, message: str, **kwargs):
        """记录结构化日志"""
        log_data = {
            "message": message,
            "timestamp": logger.opt(record=True).record["time"],
            **kwargs
        }
        
        getattr(self.logger, level.lower())(message, **log_data)
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self.log("INFO", message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.log("WARNING", message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """记录错误日志"""
        self.log("ERROR", message, **kwargs)
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self.log("DEBUG", message, **kwargs)


class LogContext:
    """日志上下文管理器"""
    
    def __init__(self, logger_name: str, **context):
        self.logger = get_logger(logger_name)
        self.context = context
    
    def __enter__(self):
        self.logger = self.logger.bind(**self.context)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.logger.error(f"Exception in context: {exc_val}")
        return False


# 便捷函数
def with_context(logger_name: str, **context):
    """创建带上下文的日志器"""
    return LogContext(logger_name, **context)


def get_structured_logger(name: str) -> StructuredLogger:
    """获取结构化日志器"""
    return StructuredLogger(name)


# 预定义的日志器
def get_strategy_logger() -> Any:
    """获取策略日志器"""
    return get_logger("strategy")


def get_data_logger() -> Any:
    """获取数据日志器"""
    return get_logger("data")


def get_engine_logger() -> Any:
    """获取引擎日志器"""
    return get_logger("engine")


def get_risk_logger() -> Any:
    """获取风险日志器"""
    return get_logger("risk")


def get_performance_logger() -> Any:
    """获取性能日志器"""
    return get_logger("performance")
