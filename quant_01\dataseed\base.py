"""
数据源基类

定义数据源的统一接口和基础功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime, date
import pandas as pd
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

from ..core.data_structures.market import OHLCV, MarketData
from ..utils.logger import get_logger

logger = get_logger(__name__)


class DataAdapter(ABC):
    """数据适配器基类
    
    用于将不同格式的数据转换为标准格式。
    """
    
    @abstractmethod
    def adapt(self, data: Any) -> pd.DataFrame:
        """适配数据格式
        
        Args:
            data: 原始数据
            
        Returns:
            标准格式的DataFrame
        """
        pass
    
    @abstractmethod
    def validate(self, data: pd.DataFrame) -> bool:
        """验证数据格式
        
        Args:
            data: 数据DataFrame
            
        Returns:
            是否符合标准格式
        """
        pass


class DataSource(ABC):
    """数据源基类
    
    定义数据源的统一接口，所有数据源都应该继承此类。
    
    优化特性：
    - 异步数据获取
    - 智能缓存机制
    - 数据质量验证
    - 错误重试机制
    - 并行数据获取
    """
    
    def __init__(
        self,
        name: str,
        adapter: Optional[DataAdapter] = None,
        cache_enabled: bool = True,
        cache_ttl: int = 3600,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        timeout: float = 30.0,
        max_workers: int = 4
    ):
        """
        初始化数据源
        
        Args:
            name: 数据源名称
            adapter: 数据适配器
            cache_enabled: 是否启用缓存
            cache_ttl: 缓存TTL（秒）
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            timeout: 请求超时时间（秒）
            max_workers: 最大工作线程数
        """
        self.name = name
        self.adapter = adapter
        self.cache_enabled = cache_enabled
        self.cache_ttl = cache_ttl
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.timeout = timeout
        self.max_workers = max_workers
        
        # 内部状态
        self._cache: Dict[str, Tuple[pd.DataFrame, float]] = {}
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._stats = {
            'requests': 0,
            'cache_hits': 0,
            'errors': 0,
            'total_time': 0.0
        }
        
        logger.info(f"数据源初始化完成: {self.name}")
    
    @abstractmethod
    def _fetch_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """获取数据的具体实现
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            **kwargs: 其他参数
            
        Returns:
            原始数据DataFrame
        """
        pass
    
    def get_data(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        use_cache: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """获取数据
        
        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            标准格式的DataFrame
        """
        start_time = time.time()
        self._stats['requests'] += 1
        
        try:
            # 生成缓存键
            cache_key = self._generate_cache_key(symbol, start_date, end_date, frequency, **kwargs)
            
            # 检查缓存
            if use_cache and self.cache_enabled and self._is_cache_valid(cache_key):
                self._stats['cache_hits'] += 1
                logger.debug(f"缓存命中: {cache_key}")
                return self._cache[cache_key][0].copy()
            
            # 获取数据
            data = self._fetch_data_with_retry(symbol, start_date, end_date, frequency, **kwargs)
            
            # 数据适配
            if self.adapter:
                data = self.adapter.adapt(data)
            
            # 数据验证
            self._validate_data(data)
            
            # 更新缓存
            if self.cache_enabled:
                self._cache[cache_key] = (data.copy(), time.time())
            
            execution_time = time.time() - start_time
            self._stats['total_time'] += execution_time
            
            logger.debug(f"数据获取完成: {symbol}, 耗时: {execution_time:.2f}秒")
            return data
            
        except Exception as e:
            self._stats['errors'] += 1
            logger.error(f"数据获取失败: {symbol}, 错误: {e}")
            raise
    
    async def get_data_async(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        use_cache: bool = True,
        **kwargs
    ) -> pd.DataFrame:
        """异步获取数据"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor,
            self.get_data,
            symbol, start_date, end_date, frequency, use_cache,
            **kwargs
        )
    
    def get_batch_data(
        self,
        symbols: List[str],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        use_cache: bool = True,
        parallel: bool = True,
        **kwargs
    ) -> Dict[str, pd.DataFrame]:
        """批量获取数据
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            use_cache: 是否使用缓存
            parallel: 是否并行获取
            **kwargs: 其他参数
            
        Returns:
            股票代码到DataFrame的映射
        """
        if not parallel:
            # 串行获取
            result = {}
            for symbol in symbols:
                try:
                    result[symbol] = self.get_data(
                        symbol, start_date, end_date, frequency, use_cache, **kwargs
                    )
                except Exception as e:
                    logger.error(f"获取数据失败: {symbol}, 错误: {e}")
            return result
        
        # 并行获取
        futures = []
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            for symbol in symbols:
                future = executor.submit(
                    self.get_data,
                    symbol, start_date, end_date, frequency, use_cache,
                    **kwargs
                )
                futures.append((symbol, future))
        
        result = {}
        for symbol, future in futures:
            try:
                result[symbol] = future.result(timeout=self.timeout)
            except Exception as e:
                logger.error(f"获取数据失败: {symbol}, 错误: {e}")
        
        return result
    
    async def get_batch_data_async(
        self,
        symbols: List[str],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        use_cache: bool = True,
        **kwargs
    ) -> Dict[str, pd.DataFrame]:
        """异步批量获取数据"""
        tasks = []
        for symbol in symbols:
            task = self.get_data_async(
                symbol, start_date, end_date, frequency, use_cache, **kwargs
            )
            tasks.append((symbol, task))
        
        result = {}
        for symbol, task in tasks:
            try:
                result[symbol] = await task
            except Exception as e:
                logger.error(f"获取数据失败: {symbol}, 错误: {e}")
        
        return result
    
    def _fetch_data_with_retry(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1d",
        **kwargs
    ) -> pd.DataFrame:
        """带重试的数据获取"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return self._fetch_data(symbol, start_date, end_date, frequency, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries:
                    logger.warning(f"数据获取失败，第{attempt + 1}次重试: {symbol}, 错误: {e}")
                    time.sleep(self.retry_delay * (2 ** attempt))  # 指数退避
                else:
                    logger.error(f"数据获取最终失败: {symbol}, 错误: {e}")
        
        raise last_exception
    
    def _generate_cache_key(
        self,
        symbol: str,
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str,
        **kwargs
    ) -> str:
        """生成缓存键"""
        key_parts = [
            self.name,
            symbol,
            str(start_date),
            str(end_date),
            frequency
        ]
        
        # 添加其他参数
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}={v}")
        
        return "|".join(key_parts)
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self._cache:
            return False
        
        _, timestamp = self._cache[cache_key]
        return time.time() - timestamp < self.cache_ttl
    
    def _validate_data(self, data: pd.DataFrame):
        """验证数据格式"""
        if data.empty:
            raise ValueError("数据为空")
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            raise ValueError(f"缺少必要列: {missing_columns}")
        
        # 检查数据类型
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(data[col]):
                raise ValueError(f"列 {col} 必须是数值类型")
        
        # 检查价格逻辑
        invalid_rows = (
            (data['high'] < data[['open', 'close']].max(axis=1)) |
            (data['low'] > data[['open', 'close']].min(axis=1)) |
            (data['volume'] < 0)
        )
        
        if invalid_rows.any():
            raise ValueError("数据包含无效的价格或成交量")
    
    def clear_cache(self):
        """清空缓存"""
        self._cache.clear()
        logger.info(f"缓存已清空: {self.name}")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存信息"""
        return {
            'cache_size': len(self._cache),
            'cache_enabled': self.cache_enabled,
            'cache_ttl': self.cache_ttl,
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self._stats.copy()
        if stats['requests'] > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / stats['requests']
            stats['avg_time'] = stats['total_time'] / stats['requests']
            stats['error_rate'] = stats['errors'] / stats['requests']
        else:
            stats['cache_hit_rate'] = 0.0
            stats['avg_time'] = 0.0
            stats['error_rate'] = 0.0
        
        return stats
    
    @abstractmethod
    def get_available_symbols(self) -> List[str]:
        """获取可用的股票代码列表"""
        pass
    
    @abstractmethod
    def get_symbol_info(self, symbol: str) -> Dict[str, Any]:
        """获取股票信息"""
        pass
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, '_executor'):
            self._executor.shutdown(wait=False)
