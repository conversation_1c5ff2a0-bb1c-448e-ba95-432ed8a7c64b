"""
策略注册器

提供装饰器方式的策略注册功能。
"""

from typing import Type, Optional, Callable, Any
from functools import wraps
from .factory import strategy_factory
from .base.strategy import BaseStrategy, StrategyConfig
from ..utils.logger import get_logger

logger = get_logger(__name__)


class StrategyRegistry:
    """策略注册器
    
    提供装饰器方式注册策略。
    """
    
    def __init__(self, factory=None):
        self.factory = factory or strategy_factory
    
    def register(
        self,
        name: str,
        config_class: Optional[Type[StrategyConfig]] = None
    ):
        """
        策略注册装饰器
        
        Args:
            name: 策略名称
            config_class: 配置类
            
        Returns:
            装饰器函数
        """
        def decorator(strategy_class: Type[BaseStrategy]):
            # 自动推断配置类
            if config_class is None:
                # 尝试从策略类的类型注解中获取配置类
                if hasattr(strategy_class, '__init__'):
                    annotations = getattr(strategy_class.__init__, '__annotations__', {})
                    config_type = annotations.get('config')
                    if config_type and issubclass(config_type, StrategyConfig):
                        inferred_config_class = config_type
                    else:
                        # 使用默认配置类
                        inferred_config_class = StrategyConfig
                else:
                    inferred_config_class = StrategyConfig
            else:
                inferred_config_class = config_class
            
            # 注册策略
            self.factory.register(name, strategy_class, inferred_config_class)
            
            logger.info(f"策略已通过装饰器注册: {name}")
            
            return strategy_class
        
        return decorator
    
    def auto_register(self, strategy_class: Type[BaseStrategy]):
        """
        自动注册策略
        
        使用策略类名作为注册名称。
        
        Args:
            strategy_class: 策略类
            
        Returns:
            策略类
        """
        name = strategy_class.__name__.lower()
        if name.endswith('strategy'):
            name = name[:-8]  # 移除'strategy'后缀
        
        return self.register(name)(strategy_class)


# 全局策略注册器实例
strategy_registry = StrategyRegistry()


# 便捷装饰器函数
def register_strategy(
    name: str,
    config_class: Optional[Type[StrategyConfig]] = None
):
    """
    策略注册装饰器
    
    Args:
        name: 策略名称
        config_class: 配置类
        
    Returns:
        装饰器函数
    """
    return strategy_registry.register(name, config_class)


def auto_register_strategy(strategy_class: Type[BaseStrategy]):
    """
    自动注册策略装饰器
    
    Args:
        strategy_class: 策略类
        
    Returns:
        策略类
    """
    return strategy_registry.auto_register(strategy_class)


# 示例用法：
"""
# 方式1：手动指定名称和配置类
@register_strategy("my_macd", MACDConfig)
class MyMACDStrategy(BaseStrategy):
    pass

# 方式2：自动推断
@auto_register_strategy
class CustomStrategy(BaseStrategy):
    pass

# 方式3：使用注册器实例
registry = StrategyRegistry()

@registry.register("custom_rsi")
class CustomRSIStrategy(BaseStrategy):
    pass
"""
