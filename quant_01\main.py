"""
Quant_01 主入口

提供简单易用的接口来运行量化策略回测。
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, date
import pandas as pd

# 添加项目根目录到路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 设置环境变量
os.environ['PYTHONPATH'] = str(current_dir)

try:
    # 导入核心模块
    from core.engine.base import QuantEngine
    from core.config.global_config import GlobalConfig
    
    # 导入数据源
    from dataseed.mock_source import MockDataSource
    from dataseed.akshare_source import AkShareDataSource
    
    # 导入策略
    from strategies.single.macd import MACDStrategy, MACDConfig
    
    # 导入工具
    from utils.logger import init_logger, get_logger
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在quant_01目录下运行此脚本")
    sys.exit(1)


class QuickStart:
    """快速开始类
    
    提供简化的接口来快速开始量化回测。
    """
    
    def __init__(self, data_source: str = "mock", config: Optional[GlobalConfig] = None):
        """
        初始化快速开始
        
        Args:
            data_source: 数据源类型 ("mock", "akshare")
            config: 全局配置
        """
        # 初始化日志
        init_logger(config)
        self.logger = get_logger(__name__)
        
        # 初始化配置
        self.config = config or GlobalConfig.create_default()
        
        # 创建引擎
        self.engine = QuantEngine(config=self.config)
        
        # 设置数据源
        self._setup_data_source(data_source)
        
        # 注册默认策略
        self._register_default_strategies()
        
        self.logger.info("Quant_01 快速开始初始化完成")
    
    def _setup_data_source(self, data_source: str):
        """设置数据源"""
        if data_source == "mock":
            self.data_source = MockDataSource(
                name="mock",
                base_price=100.0,
                volatility=0.02,
                market_state="normal"
            )
        elif data_source == "akshare":
            try:
                self.data_source = AkShareDataSource(name="akshare")
            except ImportError:
                self.logger.warning("AkShare未安装，使用Mock数据源")
                self.data_source = MockDataSource(name="mock_fallback")
        else:
            raise ValueError(f"不支持的数据源: {data_source}")
        
        self.engine.set_data_source(self.data_source)
        self.logger.info(f"数据源已设置: {data_source}")
    
    def _register_default_strategies(self):
        """注册默认策略"""
        # 注册MACD策略
        macd_strategy = MACDStrategy.create_default()
        self.engine.register_strategy(macd_strategy, "macd")
        
        # 注册快速MACD策略
        macd_fast = MACDStrategy.create_fast()
        self.engine.register_strategy(macd_fast, "macd_fast")
        
        # 注册慢速MACD策略
        macd_slow = MACDStrategy.create_slow()
        self.engine.register_strategy(macd_slow, "macd_slow")
        
        self.logger.info("默认策略已注册")
    
    def run_simple_backtest(
        self,
        strategy: str = "macd",
        symbol: str = "000001",
        start_date: str = "2023-01-01",
        end_date: str = "2023-12-31"
    ) -> Dict[str, Any]:
        """
        运行简单回测
        
        Args:
            strategy: 策略名称
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            回测结果摘要
        """
        self.logger.info(f"开始简单回测: {strategy} on {symbol}")
        
        try:
            # 运行回测
            result = self.engine.run_strategy(
                strategy=strategy,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            # 生成摘要
            summary = {
                'strategy': strategy,
                'symbol': symbol,
                'period': f"{start_date} to {end_date}",
                'total_return': f"{result.total_return:.2%}",
                'annual_return': f"{result.annual_return:.2%}",
                'volatility': f"{result.volatility:.2%}",
                'sharpe_ratio': f"{result.sharpe_ratio:.2f}",
                'max_drawdown': f"{result.max_drawdown:.2%}",
                'total_trades': result.total_trades,
                'win_rate': f"{result.win_rate:.2%}",
            }
            
            self.logger.info("简单回测完成")
            return summary
            
        except Exception as e:
            self.logger.error(f"简单回测失败: {e}")
            raise
    
    def compare_strategies(
        self,
        strategies: List[str] = None,
        symbol: str = "000001",
        start_date: str = "2023-01-01",
        end_date: str = "2023-12-31"
    ) -> pd.DataFrame:
        """
        策略对比
        
        Args:
            strategies: 策略列表
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            对比结果DataFrame
        """
        if strategies is None:
            strategies = ["macd", "macd_fast", "macd_slow"]
        
        self.logger.info(f"开始策略对比: {strategies}")
        
        try:
            # 运行对比
            results = self.engine.compare_strategies(
                strategies=strategies,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            # 构建对比表
            comparison_data = []
            for strategy_name, result in results.items():
                comparison_data.append({
                    'Strategy': strategy_name,
                    'Total Return': f"{result.total_return:.2%}",
                    'Annual Return': f"{result.annual_return:.2%}",
                    'Volatility': f"{result.volatility:.2%}",
                    'Sharpe Ratio': f"{result.sharpe_ratio:.2f}",
                    'Max Drawdown': f"{result.max_drawdown:.2%}",
                    'Total Trades': result.total_trades,
                    'Win Rate': f"{result.win_rate:.2%}",
                })
            
            comparison_df = pd.DataFrame(comparison_data)
            
            self.logger.info("策略对比完成")
            return comparison_df
            
        except Exception as e:
            self.logger.error(f"策略对比失败: {e}")
            raise
    
    def batch_backtest(
        self,
        strategy: str = "macd",
        symbols: List[str] = None,
        start_date: str = "2023-01-01",
        end_date: str = "2023-12-31"
    ) -> pd.DataFrame:
        """
        批量回测
        
        Args:
            strategy: 策略名称
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            批量回测结果DataFrame
        """
        if symbols is None:
            # 获取一些默认股票
            symbols = ["000001", "000002", "000858", "002415", "600036"]
        
        self.logger.info(f"开始批量回测: {len(symbols)}个标的")
        
        try:
            # 运行批量回测
            results = self.engine.run_batch_backtest(
                strategy=strategy,
                symbols=symbols,
                start_date=start_date,
                end_date=end_date,
                parallel=True
            )
            
            # 构建结果表
            batch_data = []
            for symbol, result in results.items():
                batch_data.append({
                    'Symbol': symbol,
                    'Total Return': f"{result.total_return:.2%}",
                    'Annual Return': f"{result.annual_return:.2%}",
                    'Volatility': f"{result.volatility:.2%}",
                    'Sharpe Ratio': f"{result.sharpe_ratio:.2f}",
                    'Max Drawdown': f"{result.max_drawdown:.2%}",
                    'Total Trades': result.total_trades,
                    'Win Rate': f"{result.win_rate:.2%}",
                })
            
            batch_df = pd.DataFrame(batch_data)
            
            self.logger.info("批量回测完成")
            return batch_df
            
        except Exception as e:
            self.logger.error(f"批量回测失败: {e}")
            raise
    
    def get_engine_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return self.engine.get_stats()
    
    def get_data_info(self) -> Dict[str, Any]:
        """获取数据信息"""
        return self.engine.get_data_info()


def demo():
    """演示函数"""
    print("=" * 60)
    print("Quant_01 量化回测引擎演示")
    print("=" * 60)
    
    # 创建快速开始实例
    qs = QuickStart(data_source="mock")
    
    print("\n1. 简单回测演示")
    print("-" * 30)
    
    # 运行简单回测
    result = qs.run_simple_backtest(
        strategy="macd",
        symbol="000001",
        start_date="2023-01-01",
        end_date="2023-12-31"
    )
    
    for key, value in result.items():
        print(f"{key}: {value}")
    
    print("\n2. 策略对比演示")
    print("-" * 30)
    
    # 策略对比
    comparison = qs.compare_strategies(
        strategies=["macd", "macd_fast", "macd_slow"],
        symbol="000001"
    )
    
    print(comparison.to_string(index=False))
    
    print("\n3. 批量回测演示")
    print("-" * 30)
    
    # 批量回测
    batch_results = qs.batch_backtest(
        strategy="macd",
        symbols=["000001", "000002", "000858"]
    )
    
    print(batch_results.to_string(index=False))
    
    print("\n4. 引擎统计信息")
    print("-" * 30)
    
    stats = qs.get_engine_stats()
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    print("\n演示完成！")
    print("=" * 60)


if __name__ == "__main__":
    # 运行演示
    demo()
