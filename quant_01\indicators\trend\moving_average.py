"""
移动平均指标

Simple Moving Average (SMA) 和 Exponential Moving Average (EMA)
"""

from typing import Union
import pandas as pd
import numpy as np

from ..base import MovingAverageBase


class SMA(MovingAverageBase):
    """简单移动平均 (Simple Moving Average)
    
    计算指定周期内的算术平均值。
    """
    
    def __init__(self, period: int):
        """
        初始化SMA指标
        
        Args:
            period: 移动平均周期
        """
        super().__init__(period)
    
    def _calculate(self, data: Union[pd.Series, pd.DataFrame]) -> pd.Series:
        """
        计算SMA
        
        Args:
            data: 输入数据
            
        Returns:
            SMA序列
        """
        if isinstance(data, pd.DataFrame):
            if 'close' not in data.columns:
                raise ValueError("DataFrame必须包含'close'列")
            prices = data['close']
        else:
            prices = data
        
        return prices.rolling(window=self.period, min_periods=1).mean()


class EMA(MovingAverageBase):
    """指数移动平均 (Exponential Moving Average)
    
    给予近期价格更高权重的移动平均。
    """
    
    def __init__(self, period: int, alpha: float = None):
        """
        初始化EMA指标
        
        Args:
            period: 移动平均周期
            alpha: 平滑因子，如果为None则使用 2/(period+1)
        """
        parameters = {'period': period}
        if alpha is not None:
            parameters['alpha'] = alpha
        
        super().__init__(period, **parameters)
        self.alpha = alpha or (2.0 / (period + 1))
    
    def _calculate(self, data: Union[pd.Series, pd.DataFrame]) -> pd.Series:
        """
        计算EMA
        
        Args:
            data: 输入数据
            
        Returns:
            EMA序列
        """
        if isinstance(data, pd.DataFrame):
            if 'close' not in data.columns:
                raise ValueError("DataFrame必须包含'close'列")
            prices = data['close']
        else:
            prices = data
        
        return prices.ewm(alpha=self.alpha, adjust=False).mean()


class WMA(MovingAverageBase):
    """加权移动平均 (Weighted Moving Average)
    
    线性加权移动平均，最新价格权重最高。
    """
    
    def __init__(self, period: int):
        """
        初始化WMA指标
        
        Args:
            period: 移动平均周期
        """
        super().__init__(period)
    
    def _calculate(self, data: Union[pd.Series, pd.DataFrame]) -> pd.Series:
        """
        计算WMA
        
        Args:
            data: 输入数据
            
        Returns:
            WMA序列
        """
        if isinstance(data, pd.DataFrame):
            if 'close' not in data.columns:
                raise ValueError("DataFrame必须包含'close'列")
            prices = data['close']
        else:
            prices = data
        
        # 计算权重
        weights = np.arange(1, self.period + 1)
        weights_sum = weights.sum()
        
        def wma_calc(x):
            if len(x) < self.period:
                # 如果数据不足，使用可用数据
                available_weights = weights[:len(x)]
                return np.average(x, weights=available_weights)
            else:
                return np.average(x, weights=weights)
        
        return prices.rolling(window=self.period, min_periods=1).apply(wma_calc, raw=True)


class DEMA(MovingAverageBase):
    """双重指数移动平均 (Double Exponential Moving Average)
    
    减少滞后的指数移动平均。
    """
    
    def __init__(self, period: int):
        """
        初始化DEMA指标
        
        Args:
            period: 移动平均周期
        """
        super().__init__(period)
    
    def _calculate(self, data: Union[pd.Series, pd.DataFrame]) -> pd.Series:
        """
        计算DEMA
        
        Args:
            data: 输入数据
            
        Returns:
            DEMA序列
        """
        if isinstance(data, pd.DataFrame):
            if 'close' not in data.columns:
                raise ValueError("DataFrame必须包含'close'列")
            prices = data['close']
        else:
            prices = data
        
        # 计算第一次EMA
        ema1 = prices.ewm(span=self.period, adjust=False).mean()
        
        # 计算第二次EMA
        ema2 = ema1.ewm(span=self.period, adjust=False).mean()
        
        # DEMA = 2 * EMA1 - EMA2
        return 2 * ema1 - ema2


class TEMA(MovingAverageBase):
    """三重指数移动平均 (Triple Exponential Moving Average)
    
    进一步减少滞后的指数移动平均。
    """
    
    def __init__(self, period: int):
        """
        初始化TEMA指标
        
        Args:
            period: 移动平均周期
        """
        super().__init__(period)
    
    def _calculate(self, data: Union[pd.Series, pd.DataFrame]) -> pd.Series:
        """
        计算TEMA
        
        Args:
            data: 输入数据
            
        Returns:
            TEMA序列
        """
        if isinstance(data, pd.DataFrame):
            if 'close' not in data.columns:
                raise ValueError("DataFrame必须包含'close'列")
            prices = data['close']
        else:
            prices = data
        
        # 计算三次EMA
        ema1 = prices.ewm(span=self.period, adjust=False).mean()
        ema2 = ema1.ewm(span=self.period, adjust=False).mean()
        ema3 = ema2.ewm(span=self.period, adjust=False).mean()
        
        # TEMA = 3 * EMA1 - 3 * EMA2 + EMA3
        return 3 * ema1 - 3 * ema2 + ema3
