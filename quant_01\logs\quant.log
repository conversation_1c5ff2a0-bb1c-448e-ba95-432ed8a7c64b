2025-05-27 12:17:46 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 12:17:46 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 12:18:08 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 12:18:08 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 12:18:49 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 12:18:49 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 12:19:31 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 12:19:31 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 12:19:32 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 12:19:32 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 12:19:32 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 12:19:32 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 12:19:32 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: test_mock
2025-05-27 12:19:32 | INFO | dataseed.mock_source:__init__:76 | Mock数据源初始化完成: test_mock, 市场状态: normal
2025-05-27 12:20:23 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 12:20:23 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 12:20:25 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 12:20:25 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 12:20:25 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 12:20:25 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 12:20:25 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: test_mock
2025-05-27 12:20:25 | INFO | dataseed.mock_source:__init__:76 | Mock数据源初始化完成: test_mock, 市场状态: normal
2025-05-27 12:20:47 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 12:20:47 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 12:20:49 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 12:20:49 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 12:20:49 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 12:20:49 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 12:20:49 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: test_mock
2025-05-27 12:20:49 | INFO | dataseed.mock_source:__init__:76 | Mock数据源初始化完成: test_mock, 市场状态: normal
2025-05-27 12:20:49 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: mock
2025-05-27 12:20:49 | INFO | dataseed.mock_source:__init__:76 | Mock数据源初始化完成: mock, 市场状态: normal
2025-05-27 12:21:19 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 12:21:19 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 12:21:21 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 12:21:21 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 12:21:21 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 12:21:21 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 12:21:21 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: test_mock
2025-05-27 12:21:21 | INFO | dataseed.mock_source:__init__:76 | Mock数据源初始化完成: test_mock, 市场状态: normal
2025-05-27 12:21:21 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: mock
2025-05-27 12:21:21 | INFO | dataseed.mock_source:__init__:76 | Mock数据源初始化完成: mock, 市场状态: normal
2025-05-27 12:21:53 | INFO | utils.logger:init_logger:83 | 日志系统初始化完成
2025-05-27 12:21:53 | INFO | utils.cache:__init__:48 | 缓存管理器初始化完成
2025-05-27 12:21:54 | INFO | dataseed.factory:register:59 | 数据源已注册: mock
2025-05-27 12:21:54 | INFO | dataseed.factory:register:59 | 数据源已注册: akshare
2025-05-27 12:21:54 | INFO | dataseed.factory:_register_builtin_sources:45 | 内置数据源注册完成
2025-05-27 12:21:54 | INFO | dataseed.factory:__init__:31 | 数据源工厂初始化完成
2025-05-27 12:21:54 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: test_mock
2025-05-27 12:21:54 | INFO | dataseed.mock_source:__init__:76 | Mock数据源初始化完成: test_mock, 市场状态: normal
2025-05-27 12:21:54 | INFO | dataseed.base:__init__:113 | 数据源初始化完成: mock
2025-05-27 12:21:54 | INFO | dataseed.mock_source:__init__:76 | Mock数据源初始化完成: mock, 市场状态: normal
