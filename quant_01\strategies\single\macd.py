"""
MACD策略

基于MACD指标的趋势跟踪策略。
"""

from typing import Dict, Any, Optional
import pandas as pd
import numpy as np
from pydantic import Field, validator

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent))

from strategies.base.strategy import BaseStrategy, StrategyConfig
from indicators.trend.macd import MACD
from utils.logger import get_logger

logger = get_logger(__name__)


class MACDConfig(StrategyConfig):
    """MACD策略配置"""

    strategy_name: str = Field(default="MACD", description="策略名称")

    # MACD参数
    fast_period: int = Field(default=12, description="快线周期", ge=1, le=50)
    slow_period: int = Field(default=26, description="慢线周期", ge=1, le=100)
    signal_period: int = Field(default=9, description="信号线周期", ge=1, le=50)

    # 信号参数
    signal_threshold: float = Field(default=0.0, description="信号阈值")
    min_signal_strength: float = Field(default=0.1, description="最小信号强度", ge=0.0, le=1.0)

    # 过滤参数
    use_price_filter: bool = Field(default=True, description="是否使用价格过滤")
    min_price: float = Field(default=1.0, description="最小价格", ge=0.0)
    max_price: float = Field(default=1000.0, description="最大价格", ge=0.0)

    use_volume_filter: bool = Field(default=True, description="是否使用成交量过滤")
    min_volume_ratio: float = Field(default=0.5, description="最小成交量比例", ge=0.0, le=10.0)

    # 止损止盈
    use_stop_loss: bool = Field(default=False, description="是否使用止损")
    stop_loss_pct: float = Field(default=0.05, description="止损百分比", ge=0.0, le=1.0)

    use_take_profit: bool = Field(default=False, description="是否使用止盈")
    take_profit_pct: float = Field(default=0.10, description="止盈百分比", ge=0.0, le=1.0)

    @validator('slow_period')
    def validate_slow_period(cls, v, values):
        if 'fast_period' in values and v <= values['fast_period']:
            raise ValueError('慢线周期必须大于快线周期')
        return v

    @validator('max_price')
    def validate_max_price(cls, v, values):
        if 'min_price' in values and v <= values['min_price']:
            raise ValueError('最大价格必须大于最小价格')
        return v


class MACDStrategy(BaseStrategy):
    """MACD策略

    策略逻辑：
    1. 计算MACD指标（MACD线、信号线、柱状图）
    2. 当MACD线上穿信号线时产生买入信号
    3. 当MACD线下穿信号线时产生卖出信号
    4. 根据柱状图强度计算信号强度
    5. 可选的价格和成交量过滤
    6. 可选的止损止盈

    优化特性：
    - 向量化计算，高性能
    - 多重信号过滤
    - 动态信号强度计算
    - 完整的参数验证
    """

    def __init__(self, config: Optional[MACDConfig] = None):
        """
        初始化MACD策略

        Args:
            config: MACD策略配置
        """
        if config is None:
            config = MACDConfig()

        super().__init__(config)
        self.config: MACDConfig = config

        # 初始化MACD指标
        self.macd_indicator = MACD(
            fast_period=config.fast_period,
            slow_period=config.slow_period,
            signal_period=config.signal_period
        )

        logger.info(f"MACD策略初始化完成: fast={config.fast_period}, slow={config.slow_period}, signal={config.signal_period}")

    def _precompute_indicators(self, data: pd.DataFrame):
        """预计算MACD指标"""
        # 计算MACD
        macd_result = self.macd_indicator.calculate(data['close'])

        self._indicators_cache['macd'] = macd_result['macd']
        self._indicators_cache['signal'] = macd_result['signal']
        self._indicators_cache['histogram'] = macd_result['histogram']

        # 计算成交量移动平均（用于成交量过滤）
        if self.config.use_volume_filter:
            volume_ma = data['volume'].rolling(window=20).mean()
            self._indicators_cache['volume_ma'] = volume_ma

        logger.debug("MACD指标预计算完成")

    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成MACD交易信号

        Args:
            data: 价格数据DataFrame

        Returns:
            信号DataFrame
        """
        # 获取预计算的指标
        macd = self._indicators_cache['macd']
        signal = self._indicators_cache['signal']
        histogram = self._indicators_cache['histogram']

        # 初始化信号DataFrame
        signals = pd.DataFrame(index=data.index)
        signals['signal'] = 0
        signals['strength'] = 0.0
        signals['price'] = data['close']
        signals['macd'] = macd
        signals['signal_line'] = signal
        signals['histogram'] = histogram

        # 计算MACD穿越信号
        macd_cross_up = (macd > signal) & (macd.shift(1) <= signal.shift(1))
        macd_cross_down = (macd < signal) & (macd.shift(1) >= signal.shift(1))

        # 生成基础信号
        signals.loc[macd_cross_up, 'signal'] = 1  # 买入信号
        signals.loc[macd_cross_down, 'signal'] = -1  # 卖出信号

        # 计算信号强度
        signals['strength'] = self._calculate_signal_strength(data, signals)

        # 应用过滤器
        signals = self._apply_filters(data, signals)

        # 应用信号阈值
        weak_signals = signals['strength'] < self.config.min_signal_strength
        signals.loc[weak_signals, 'signal'] = 0
        signals.loc[weak_signals, 'strength'] = 0.0

        # 统计信号
        buy_signals = (signals['signal'] == 1).sum()
        sell_signals = (signals['signal'] == -1).sum()

        logger.info(f"MACD信号生成完成: 买入信号={buy_signals}, 卖出信号={sell_signals}")

        return signals

    def _calculate_signal_strength(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.Series:
        """计算信号强度"""
        strength = pd.Series(0.0, index=data.index)

        # 基于MACD柱状图计算强度
        histogram = signals['histogram']
        histogram_abs = histogram.abs()

        # 归一化柱状图强度（使用滚动最大值）
        histogram_max = histogram_abs.rolling(window=50, min_periods=10).max()
        normalized_strength = histogram_abs / histogram_max.where(histogram_max > 0, 1.0)

        # 限制强度范围
        strength = normalized_strength.clip(0.0, 1.0)

        # 对于有信号的位置，确保最小强度
        has_signal = signals['signal'] != 0
        strength.loc[has_signal] = strength.loc[has_signal].clip(self.config.min_signal_strength, 1.0)

        return strength

    def _apply_filters(self, data: pd.DataFrame, signals: pd.DataFrame) -> pd.DataFrame:
        """应用信号过滤器"""
        filtered_signals = signals.copy()

        # 价格过滤
        if self.config.use_price_filter:
            price_filter = (
                (data['close'] >= self.config.min_price) &
                (data['close'] <= self.config.max_price)
            )

            filtered_signals.loc[~price_filter, 'signal'] = 0
            filtered_signals.loc[~price_filter, 'strength'] = 0.0

        # 成交量过滤
        if self.config.use_volume_filter and 'volume_ma' in self._indicators_cache:
            volume_ma = self._indicators_cache['volume_ma']
            volume_filter = data['volume'] >= (volume_ma * self.config.min_volume_ratio)

            filtered_signals.loc[~volume_filter, 'signal'] = 0
            filtered_signals.loc[~volume_filter, 'strength'] = 0.0

        # 趋势过滤（可选）
        # 可以添加更多过滤条件

        return filtered_signals

    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'trend_following',
            'indicators': ['MACD'],
            'parameters': {
                'fast_period': self.config.fast_period,
                'slow_period': self.config.slow_period,
                'signal_period': self.config.signal_period,
                'signal_threshold': self.config.signal_threshold,
                'min_signal_strength': self.config.min_signal_strength,
            },
            'filters': {
                'price_filter': self.config.use_price_filter,
                'volume_filter': self.config.use_volume_filter,
            },
            'risk_management': {
                'stop_loss': self.config.use_stop_loss,
                'take_profit': self.config.use_take_profit,
            }
        }

    def optimize_parameters(self, data: pd.DataFrame, param_ranges: Dict[str, Any]) -> Dict[str, Any]:
        """参数优化（简化版本）"""
        best_params = {}
        best_score = -np.inf

        # 这里可以实现网格搜索或其他优化算法
        # 为了简化，这里只返回当前参数

        return {
            'best_params': {
                'fast_period': self.config.fast_period,
                'slow_period': self.config.slow_period,
                'signal_period': self.config.signal_period,
            },
            'best_score': best_score,
            'optimization_history': []
        }

    @classmethod
    def create_default(cls) -> 'MACDStrategy':
        """创建默认配置的MACD策略"""
        return cls(MACDConfig())

    @classmethod
    def create_fast(cls) -> 'MACDStrategy':
        """创建快速MACD策略"""
        config = MACDConfig(
            fast_period=8,
            slow_period=21,
            signal_period=5,
            strategy_name="MACD_Fast"
        )
        return cls(config)

    @classmethod
    def create_slow(cls) -> 'MACDStrategy':
        """创建慢速MACD策略"""
        config = MACDConfig(
            fast_period=16,
            slow_period=35,
            signal_period=12,
            strategy_name="MACD_Slow"
        )
        return cls(config)
