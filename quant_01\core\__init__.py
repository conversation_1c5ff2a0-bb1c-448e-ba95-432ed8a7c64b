"""
核心模块

包含回测引擎、配置管理、数据结构等核心功能。
"""

from .data_structures.market import OHLCV
from .data_structures.trading import Order, Position, Trade, OrderSide, OrderStatus, PositionSide
from .data_structures.portfolio import Portfolio
from .config.global_config import GlobalConfig
from .engine.base import QuantEngine

__all__ = [
    # 数据结构
    "OHLCV",
    "Order",
    "Position", 
    "Trade",
    "Portfolio",
    "OrderSide",
    "OrderStatus",
    "PositionSide",
    
    # 配置
    "GlobalConfig",
    
    # 引擎
    "QuantEngine",
]
