"""
策略工厂

提供策略的动态创建和管理功能。
"""

from typing import Dict, Type, Any, Optional, List
from .base.strategy import BaseStrategy, StrategyConfig
from ..utils.logger import get_logger

logger = get_logger(__name__)


class StrategyFactory:
    """策略工厂

    负责策略的注册、创建和管理。
    """

    def __init__(self):
        self._strategies: Dict[str, Type[BaseStrategy]] = {}
        self._configs: Dict[str, Type[StrategyConfig]] = {}

        # 注册内置策略
        self._register_builtin_strategies()

        logger.info("策略工厂初始化完成")

    def _register_builtin_strategies(self):
        """注册内置策略"""
        try:
            from .single.macd import MACDStrategy, MACDConfig
            self.register("macd", MACDStrategy, MACDConfig)

            from .single.rsi import RSIStrategy, RSIConfig
            self.register("rsi", RSIStrategy, RSIConfig)

            logger.info("内置策略注册完成")
        except ImportError as e:
            logger.warning(f"部分内置策略注册失败: {e}")

    def register(
        self,
        name: str,
        strategy_class: Type[BaseStrategy],
        config_class: Type[StrategyConfig]
    ):
        """
        注册策略

        Args:
            name: 策略名称
            strategy_class: 策略类
            config_class: 配置类
        """
        if not issubclass(strategy_class, BaseStrategy):
            raise ValueError(f"策略类必须继承BaseStrategy: {strategy_class}")

        if not issubclass(config_class, StrategyConfig):
            raise ValueError(f"配置类必须继承StrategyConfig: {config_class}")

        self._strategies[name] = strategy_class
        self._configs[name] = config_class

        logger.info(f"策略已注册: {name}")

    def create(
        self,
        name: str,
        config: Optional[StrategyConfig] = None,
        **kwargs
    ) -> BaseStrategy:
        """
        创建策略实例

        Args:
            name: 策略名称
            config: 策略配置
            **kwargs: 配置参数

        Returns:
            策略实例
        """
        if name not in self._strategies:
            raise ValueError(f"未注册的策略: {name}")

        strategy_class = self._strategies[name]
        config_class = self._configs[name]

        # 创建配置
        if config is None:
            if kwargs:
                config = config_class(**kwargs)
            else:
                config = config_class()

        # 创建策略实例
        strategy = strategy_class(config)

        logger.info(f"策略实例已创建: {name}")
        return strategy

    def create_with_params(
        self,
        name: str,
        **params
    ) -> BaseStrategy:
        """
        使用参数创建策略

        Args:
            name: 策略名称
            **params: 策略参数

        Returns:
            策略实例
        """
        return self.create(name, **params)

    def get_available_strategies(self) -> List[str]:
        """获取可用策略列表"""
        return list(self._strategies.keys())

    def get_strategy_info(self, name: str) -> Dict[str, Any]:
        """
        获取策略信息

        Args:
            name: 策略名称

        Returns:
            策略信息
        """
        if name not in self._strategies:
            raise ValueError(f"未注册的策略: {name}")

        strategy_class = self._strategies[name]
        config_class = self._configs[name]

        # 获取配置字段信息
        config_fields = {}
        if hasattr(config_class, 'model_fields'):
            # Pydantic v2
            for field_name, field_info in config_class.model_fields.items():
                config_fields[field_name] = {
                    'type': str(field_info.annotation),
                    'default': field_info.default,
                    'description': field_info.description or '',
                }
        elif hasattr(config_class, '__fields__'):
            # Pydantic v1
            for field_name, field_info in config_class.__fields__.items():
                config_fields[field_name] = {
                    'type': str(field_info.type_),
                    'default': field_info.default,
                    'description': field_info.field_info.description or '',
                }

        return {
            'name': name,
            'strategy_class': strategy_class.__name__,
            'config_class': config_class.__name__,
            'module': strategy_class.__module__,
            'doc': strategy_class.__doc__ or '',
            'config_fields': config_fields,
        }

    def is_registered(self, name: str) -> bool:
        """检查策略是否已注册"""
        return name in self._strategies

    def unregister(self, name: str):
        """注销策略"""
        if name in self._strategies:
            del self._strategies[name]
            del self._configs[name]
            logger.info(f"策略已注销: {name}")

    def clear(self):
        """清空所有注册的策略"""
        self._strategies.clear()
        self._configs.clear()
        logger.info("所有策略已清空")

    def __len__(self) -> int:
        """返回注册策略数量"""
        return len(self._strategies)

    def __contains__(self, name: str) -> bool:
        """检查策略是否存在"""
        return name in self._strategies

    def __iter__(self):
        """迭代策略名称"""
        return iter(self._strategies.keys())


# 全局策略工厂实例
strategy_factory = StrategyFactory()
